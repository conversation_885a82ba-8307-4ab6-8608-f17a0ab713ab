/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1alpha3

// This file contains a collection of methods that can be used from go-restful to
// generate Swagger API documentation for its models. Please read this PR for more
// information on the implementation: https://github.com/emicklei/go-restful/pull/215
//
// TODOs are ignored from the parser (e.g. TODO(andronat):... || TODO:...) if and only if
// they are on one line! For multiple line or blocks that you want to ignore use ---.
// Any context after a --- is ignored.
//
// Those methods can be generated by using hack/update-codegen.sh

// AUTO-GENERATED FUNCTIONS START HERE. DO NOT EDIT.
var map_AllocatedDeviceStatus = map[string]string{
	"":            "AllocatedDeviceStatus contains the status of an allocated device, if the driver chooses to report it. This may include driver-specific information.",
	"driver":      "Driver specifies the name of the DRA driver whose kubelet plugin should be invoked to process the allocation once the claim is needed on a node.\n\nMust be a DNS subdomain and should end with a DNS domain owned by the vendor of the driver.",
	"pool":        "This name together with the driver name and the device name field identify which device was allocated (`<driver name>/<pool name>/<device name>`).\n\nMust not be longer than 253 characters and may contain one or more DNS sub-domains separated by slashes.",
	"device":      "Device references one device instance via its name in the driver's resource pool. It must be a DNS label.",
	"conditions":  "Conditions contains the latest observation of the device's state. If the device has been configured according to the class and claim config references, the `Ready` condition should be True.\n\nMust not contain more than 8 entries.",
	"data":        "Data contains arbitrary driver-specific data.\n\nThe length of the raw data must be smaller or equal to 10 Ki.",
	"networkData": "NetworkData contains network-related information specific to the device.",
}

func (AllocatedDeviceStatus) SwaggerDoc() map[string]string {
	return map_AllocatedDeviceStatus
}

var map_AllocationResult = map[string]string{
	"":             "AllocationResult contains attributes of an allocated resource.",
	"devices":      "Devices is the result of allocating devices.",
	"nodeSelector": "NodeSelector defines where the allocated resources are available. If unset, they are available everywhere.",
}

func (AllocationResult) SwaggerDoc() map[string]string {
	return map_AllocationResult
}

var map_BasicDevice = map[string]string{
	"":                 "BasicDevice defines one device instance.",
	"attributes":       "Attributes defines the set of attributes for this device. The name of each attribute must be unique in that set.\n\nThe maximum number of attributes and capacities combined is 32.",
	"capacity":         "Capacity defines the set of capacities for this device. The name of each capacity must be unique in that set.\n\nThe maximum number of attributes and capacities combined is 32.",
	"consumesCounters": "ConsumesCounters defines a list of references to sharedCounters and the set of counters that the device will consume from those counter sets.\n\nThere can only be a single entry per counterSet.\n\nThe total number of device counter consumption entries must be <= 32. In addition, the total number in the entire ResourceSlice must be <= 1024 (for example, 64 devices with 16 counters each).",
	"nodeName":         "NodeName identifies the node where the device is available.\n\nMust only be set if Spec.PerDeviceNodeSelection is set to true. At most one of NodeName, NodeSelector and AllNodes can be set.",
	"nodeSelector":     "NodeSelector defines the nodes where the device is available.\n\nMust only be set if Spec.PerDeviceNodeSelection is set to true. At most one of NodeName, NodeSelector and AllNodes can be set.",
	"allNodes":         "AllNodes indicates that all nodes have access to the device.\n\nMust only be set if Spec.PerDeviceNodeSelection is set to true. At most one of NodeName, NodeSelector and AllNodes can be set.",
	"taints":           "If specified, these are the driver-defined taints.\n\nThe maximum number of taints is 4.\n\nThis is an alpha field and requires enabling the DRADeviceTaints feature gate.",
}

func (BasicDevice) SwaggerDoc() map[string]string {
	return map_BasicDevice
}

var map_CELDeviceSelector = map[string]string{
	"":           "CELDeviceSelector contains a CEL expression for selecting a device.",
	"expression": "Expression is a CEL expression which evaluates a single device. It must evaluate to true when the device under consideration satisfies the desired criteria, and false when it does not. Any other result is an error and causes allocation of devices to abort.\n\nThe expression's input is an object named \"device\", which carries the following properties:\n - driver (string): the name of the driver which defines this device.\n - attributes (map[string]object): the device's attributes, grouped by prefix\n   (e.g. device.attributes[\"dra.example.com\"] evaluates to an object with all\n   of the attributes which were prefixed by \"dra.example.com\".\n - capacity (map[string]object): the device's capacities, grouped by prefix.\n\nExample: Consider a device with driver=\"dra.example.com\", which exposes two attributes named \"model\" and \"ext.example.com/family\" and which exposes one capacity named \"modules\". This input to this expression would have the following fields:\n\n    device.driver\n    device.attributes[\"dra.example.com\"].model\n    device.attributes[\"ext.example.com\"].family\n    device.capacity[\"dra.example.com\"].modules\n\nThe device.driver field can be used to check for a specific driver, either as a high-level precondition (i.e. you only want to consider devices from this driver) or as part of a multi-clause expression that is meant to consider devices from different drivers.\n\nThe value type of each attribute is defined by the device definition, and users who write these expressions must consult the documentation for their specific drivers. The value type of each capacity is Quantity.\n\nIf an unknown prefix is used as a lookup in either device.attributes or device.capacity, an empty map will be returned. Any reference to an unknown field will cause an evaluation error and allocation to abort.\n\nA robust expression should check for the existence of attributes before referencing them.\n\nFor ease of use, the cel.bind() function is enabled, and can be used to simplify expressions that access multiple attributes with the same domain. For example:\n\n    cel.bind(dra, device.attributes[\"dra.example.com\"], dra.someBool && dra.anotherBool)\n\nThe length of the expression must be smaller or equal to 10 Ki. The cost of evaluating it is also limited based on the estimated number of logical steps.",
}

func (CELDeviceSelector) SwaggerDoc() map[string]string {
	return map_CELDeviceSelector
}

var map_Counter = map[string]string{
	"":      "Counter describes a quantity associated with a device.",
	"value": "Value defines how much of a certain device counter is available.",
}

func (Counter) SwaggerDoc() map[string]string {
	return map_Counter
}

var map_CounterSet = map[string]string{
	"":         "CounterSet defines a named set of counters that are available to be used by devices defined in the ResourceSlice.\n\nThe counters are not allocatable by themselves, but can be referenced by devices. When a device is allocated, the portion of counters it uses will no longer be available for use by other devices.",
	"name":     "CounterSet is the name of the set from which the counters defined will be consumed.",
	"counters": "Counters defines the counters that will be consumed by the device. The name of each counter must be unique in that set and must be a DNS label.\n\nTo ensure this uniqueness, capacities defined by the vendor must be listed without the driver name as domain prefix in their name. All others must be listed with their domain prefix.\n\nThe maximum number of counters is 32.",
}

func (CounterSet) SwaggerDoc() map[string]string {
	return map_CounterSet
}

var map_Device = map[string]string{
	"":      "Device represents one individual hardware instance that can be selected based on its attributes. Besides the name, exactly one field must be set.",
	"name":  "Name is unique identifier among all devices managed by the driver in the pool. It must be a DNS label.",
	"basic": "Basic defines one device instance.",
}

func (Device) SwaggerDoc() map[string]string {
	return map_Device
}

var map_DeviceAllocationConfiguration = map[string]string{
	"":         "DeviceAllocationConfiguration gets embedded in an AllocationResult.",
	"source":   "Source records whether the configuration comes from a class and thus is not something that a normal user would have been able to set or from a claim.",
	"requests": "Requests lists the names of requests where the configuration applies. If empty, its applies to all requests.\n\nReferences to subrequests must include the name of the main request and may include the subrequest using the format <main request>[/<subrequest>]. If just the main request is given, the configuration applies to all subrequests.",
}

func (DeviceAllocationConfiguration) SwaggerDoc() map[string]string {
	return map_DeviceAllocationConfiguration
}

var map_DeviceAllocationResult = map[string]string{
	"":        "DeviceAllocationResult is the result of allocating devices.",
	"results": "Results lists all allocated devices.",
	"config":  "This field is a combination of all the claim and class configuration parameters. Drivers can distinguish between those based on a flag.\n\nThis includes configuration parameters for drivers which have no allocated devices in the result because it is up to the drivers which configuration parameters they support. They can silently ignore unknown configuration parameters.",
}

func (DeviceAllocationResult) SwaggerDoc() map[string]string {
	return map_DeviceAllocationResult
}

var map_DeviceAttribute = map[string]string{
	"":        "DeviceAttribute must have exactly one field set.",
	"int":     "IntValue is a number.",
	"bool":    "BoolValue is a true/false value.",
	"string":  "StringValue is a string. Must not be longer than 64 characters.",
	"version": "VersionValue is a semantic version according to semver.org spec 2.0.0. Must not be longer than 64 characters.",
}

func (DeviceAttribute) SwaggerDoc() map[string]string {
	return map_DeviceAttribute
}

var map_DeviceClaim = map[string]string{
	"":            "DeviceClaim defines how to request devices with a ResourceClaim.",
	"requests":    "Requests represent individual requests for distinct devices which must all be satisfied. If empty, nothing needs to be allocated.",
	"constraints": "These constraints must be satisfied by the set of devices that get allocated for the claim.",
	"config":      "This field holds configuration for multiple potential drivers which could satisfy requests in this claim. It is ignored while allocating the claim.",
}

func (DeviceClaim) SwaggerDoc() map[string]string {
	return map_DeviceClaim
}

var map_DeviceClaimConfiguration = map[string]string{
	"":         "DeviceClaimConfiguration is used for configuration parameters in DeviceClaim.",
	"requests": "Requests lists the names of requests where the configuration applies. If empty, it applies to all requests.\n\nReferences to subrequests must include the name of the main request and may include the subrequest using the format <main request>[/<subrequest>]. If just the main request is given, the configuration applies to all subrequests.",
}

func (DeviceClaimConfiguration) SwaggerDoc() map[string]string {
	return map_DeviceClaimConfiguration
}

var map_DeviceClass = map[string]string{
	"":         "DeviceClass is a vendor- or admin-provided resource that contains device configuration and selectors. It can be referenced in the device requests of a claim to apply these presets. Cluster scoped.\n\nThis is an alpha type and requires enabling the DynamicResourceAllocation feature gate.",
	"metadata": "Standard object metadata",
	"spec":     "Spec defines what can be allocated and how to configure it.\n\nThis is mutable. Consumers have to be prepared for classes changing at any time, either because they get updated or replaced. Claim allocations are done once based on whatever was set in classes at the time of allocation.\n\nChanging the spec automatically increments the metadata.generation number.",
}

func (DeviceClass) SwaggerDoc() map[string]string {
	return map_DeviceClass
}

var map_DeviceClassConfiguration = map[string]string{
	"": "DeviceClassConfiguration is used in DeviceClass.",
}

func (DeviceClassConfiguration) SwaggerDoc() map[string]string {
	return map_DeviceClassConfiguration
}

var map_DeviceClassList = map[string]string{
	"":         "DeviceClassList is a collection of classes.",
	"metadata": "Standard list metadata",
	"items":    "Items is the list of resource classes.",
}

func (DeviceClassList) SwaggerDoc() map[string]string {
	return map_DeviceClassList
}

var map_DeviceClassSpec = map[string]string{
	"":          "DeviceClassSpec is used in a [DeviceClass] to define what can be allocated and how to configure it.",
	"selectors": "Each selector must be satisfied by a device which is claimed via this class.",
	"config":    "Config defines configuration parameters that apply to each device that is claimed via this class. Some classses may potentially be satisfied by multiple drivers, so each instance of a vendor configuration applies to exactly one driver.\n\nThey are passed to the driver, but are not considered while allocating the claim.",
}

func (DeviceClassSpec) SwaggerDoc() map[string]string {
	return map_DeviceClassSpec
}

var map_DeviceConfiguration = map[string]string{
	"":       "DeviceConfiguration must have exactly one field set. It gets embedded inline in some other structs which have other fields, so field names must not conflict with those.",
	"opaque": "Opaque provides driver-specific configuration parameters.",
}

func (DeviceConfiguration) SwaggerDoc() map[string]string {
	return map_DeviceConfiguration
}

var map_DeviceConstraint = map[string]string{
	"":               "DeviceConstraint must have exactly one field set besides Requests.",
	"requests":       "Requests is a list of the one or more requests in this claim which must co-satisfy this constraint. If a request is fulfilled by multiple devices, then all of the devices must satisfy the constraint. If this is not specified, this constraint applies to all requests in this claim.\n\nReferences to subrequests must include the name of the main request and may include the subrequest using the format <main request>[/<subrequest>]. If just the main request is given, the constraint applies to all subrequests.",
	"matchAttribute": "MatchAttribute requires that all devices in question have this attribute and that its type and value are the same across those devices.\n\nFor example, if you specified \"dra.example.com/numa\" (a hypothetical example!), then only devices in the same NUMA node will be chosen. A device which does not have that attribute will not be chosen. All devices should use a value of the same type for this attribute because that is part of its specification, but if one device doesn't, then it also will not be chosen.\n\nMust include the domain qualifier.",
}

func (DeviceConstraint) SwaggerDoc() map[string]string {
	return map_DeviceConstraint
}

var map_DeviceCounterConsumption = map[string]string{
	"":           "DeviceCounterConsumption defines a set of counters that a device will consume from a CounterSet.",
	"counterSet": "CounterSet defines the set from which the counters defined will be consumed.",
	"counters":   "Counters defines the Counter that will be consumed by the device.\n\nThe maximum number counters in a device is 32. In addition, the maximum number of all counters in all devices is 1024 (for example, 64 devices with 16 counters each).",
}

func (DeviceCounterConsumption) SwaggerDoc() map[string]string {
	return map_DeviceCounterConsumption
}

var map_DeviceRequest = map[string]string{
	"":                "DeviceRequest is a request for devices required for a claim. This is typically a request for a single resource like a device, but can also ask for several identical devices.",
	"name":            "Name can be used to reference this request in a pod.spec.containers[].resources.claims entry and in a constraint of the claim.\n\nMust be a DNS label.",
	"deviceClassName": "DeviceClassName references a specific DeviceClass, which can define additional configuration and selectors to be inherited by this request.\n\nA class is required if no subrequests are specified in the firstAvailable list and no class can be set if subrequests are specified in the firstAvailable list. Which classes are available depends on the cluster.\n\nAdministrators may use this to restrict which devices may get requested by only installing classes with selectors for permitted devices. If users are free to request anything without restrictions, then administrators can create an empty DeviceClass for users to reference.",
	"selectors":       "Selectors define criteria which must be satisfied by a specific device in order for that device to be considered for this request. All selectors must be satisfied for a device to be considered.\n\nThis field can only be set when deviceClassName is set and no subrequests are specified in the firstAvailable list.",
	"allocationMode":  "AllocationMode and its related fields define how devices are allocated to satisfy this request. Supported values are:\n\n- ExactCount: This request is for a specific number of devices.\n  This is the default. The exact number is provided in the\n  count field.\n\n- All: This request is for all of the matching devices in a pool.\n  At least one device must exist on the node for the allocation to succeed.\n  Allocation will fail if some devices are already allocated,\n  unless adminAccess is requested.\n\nIf AllocationMode is not specified, the default mode is ExactCount. If the mode is ExactCount and count is not specified, the default count is one. Any other requests must specify this field.\n\nThis field can only be set when deviceClassName is set and no subrequests are specified in the firstAvailable list.\n\nMore modes may get added in the future. Clients must refuse to handle requests with unknown modes.",
	"count":           "Count is used only when the count mode is \"ExactCount\". Must be greater than zero. If AllocationMode is ExactCount and this field is not specified, the default is one.\n\nThis field can only be set when deviceClassName is set and no subrequests are specified in the firstAvailable list.",
	"adminAccess":     "AdminAccess indicates that this is a claim for administrative access to the device(s). Claims with AdminAccess are expected to be used for monitoring or other management services for a device.  They ignore all ordinary claims to the device with respect to access modes and any resource allocations.\n\nThis field can only be set when deviceClassName is set and no subrequests are specified in the firstAvailable list.\n\nThis is an alpha field and requires enabling the DRAAdminAccess feature gate. Admin access is disabled if this field is unset or set to false, otherwise it is enabled.",
	"firstAvailable":  "FirstAvailable contains subrequests, of which exactly one will be satisfied by the scheduler to satisfy this request. It tries to satisfy them in the order in which they are listed here. So if there are two entries in the list, the scheduler will only check the second one if it determines that the first one cannot be used.\n\nThis field may only be set in the entries of DeviceClaim.Requests.\n\nDRA does not yet implement scoring, so the scheduler will select the first set of devices that satisfies all the requests in the claim. And if the requirements can be satisfied on more than one node, other scheduling features will determine which node is chosen. This means that the set of devices allocated to a claim might not be the optimal set available to the cluster. Scoring will be implemented later.",
	"tolerations":     "If specified, the request's tolerations.\n\nTolerations for NoSchedule are required to allocate a device which has a taint with that effect. The same applies to NoExecute.\n\nIn addition, should any of the allocated devices get tainted with NoExecute after allocation and that effect is not tolerated, then all pods consuming the ResourceClaim get deleted to evict them. The scheduler will not let new pods reserve the claim while it has these tainted devices. Once all pods are evicted, the claim will get deallocated.\n\nThe maximum number of tolerations is 16.\n\nThis field can only be set when deviceClassName is set and no subrequests are specified in the firstAvailable list.\n\nThis is an alpha field and requires enabling the DRADeviceTaints feature gate.",
}

func (DeviceRequest) SwaggerDoc() map[string]string {
	return map_DeviceRequest
}

var map_DeviceRequestAllocationResult = map[string]string{
	"":            "DeviceRequestAllocationResult contains the allocation result for one request.",
	"request":     "Request is the name of the request in the claim which caused this device to be allocated. If it references a subrequest in the firstAvailable list on a DeviceRequest, this field must include both the name of the main request and the subrequest using the format <main request>/<subrequest>.\n\nMultiple devices may have been allocated per request.",
	"driver":      "Driver specifies the name of the DRA driver whose kubelet plugin should be invoked to process the allocation once the claim is needed on a node.\n\nMust be a DNS subdomain and should end with a DNS domain owned by the vendor of the driver.",
	"pool":        "This name together with the driver name and the device name field identify which device was allocated (`<driver name>/<pool name>/<device name>`).\n\nMust not be longer than 253 characters and may contain one or more DNS sub-domains separated by slashes.",
	"device":      "Device references one device instance via its name in the driver's resource pool. It must be a DNS label.",
	"adminAccess": "AdminAccess indicates that this device was allocated for administrative access. See the corresponding request field for a definition of mode.\n\nThis is an alpha field and requires enabling the DRAAdminAccess feature gate. Admin access is disabled if this field is unset or set to false, otherwise it is enabled.",
	"tolerations": "A copy of all tolerations specified in the request at the time when the device got allocated.\n\nThe maximum number of tolerations is 16.\n\nThis is an alpha field and requires enabling the DRADeviceTaints feature gate.",
}

func (DeviceRequestAllocationResult) SwaggerDoc() map[string]string {
	return map_DeviceRequestAllocationResult
}

var map_DeviceSelector = map[string]string{
	"":    "DeviceSelector must have exactly one field set.",
	"cel": "CEL contains a CEL expression for selecting a device.",
}

func (DeviceSelector) SwaggerDoc() map[string]string {
	return map_DeviceSelector
}

var map_DeviceSubRequest = map[string]string{
	"":                "DeviceSubRequest describes a request for device provided in the claim.spec.devices.requests[].firstAvailable array. Each is typically a request for a single resource like a device, but can also ask for several identical devices.\n\nDeviceSubRequest is similar to Request, but doesn't expose the AdminAccess or FirstAvailable fields, as those can only be set on the top-level request. AdminAccess is not supported for requests with a prioritized list, and recursive FirstAvailable fields are not supported.",
	"name":            "Name can be used to reference this subrequest in the list of constraints or the list of configurations for the claim. References must use the format <main request>/<subrequest>.\n\nMust be a DNS label.",
	"deviceClassName": "DeviceClassName references a specific DeviceClass, which can define additional configuration and selectors to be inherited by this subrequest.\n\nA class is required. Which classes are available depends on the cluster.\n\nAdministrators may use this to restrict which devices may get requested by only installing classes with selectors for permitted devices. If users are free to request anything without restrictions, then administrators can create an empty DeviceClass for users to reference.",
	"selectors":       "Selectors define criteria which must be satisfied by a specific device in order for that device to be considered for this request. All selectors must be satisfied for a device to be considered.",
	"allocationMode":  "AllocationMode and its related fields define how devices are allocated to satisfy this request. Supported values are:\n\n- ExactCount: This request is for a specific number of devices.\n  This is the default. The exact number is provided in the\n  count field.\n\n- All: This request is for all of the matching devices in a pool.\n  Allocation will fail if some devices are already allocated,\n  unless adminAccess is requested.\n\nIf AllocationMode is not specified, the default mode is ExactCount. If the mode is ExactCount and count is not specified, the default count is one. Any other requests must specify this field.\n\nMore modes may get added in the future. Clients must refuse to handle requests with unknown modes.",
	"count":           "Count is used only when the count mode is \"ExactCount\". Must be greater than zero. If AllocationMode is ExactCount and this field is not specified, the default is one.",
	"tolerations":     "If specified, the request's tolerations.\n\nTolerations for NoSchedule are required to allocate a device which has a taint with that effect. The same applies to NoExecute.\n\nIn addition, should any of the allocated devices get tainted with NoExecute after allocation and that effect is not tolerated, then all pods consuming the ResourceClaim get deleted to evict them. The scheduler will not let new pods reserve the claim while it has these tainted devices. Once all pods are evicted, the claim will get deallocated.\n\nThe maximum number of tolerations is 16.\n\nThis is an alpha field and requires enabling the DRADeviceTaints feature gate.",
}

func (DeviceSubRequest) SwaggerDoc() map[string]string {
	return map_DeviceSubRequest
}

var map_DeviceTaint = map[string]string{
	"":          "The device this taint is attached to has the \"effect\" on any claim which does not tolerate the taint and, through the claim, to pods using the claim.",
	"key":       "The taint key to be applied to a device. Must be a label name.",
	"value":     "The taint value corresponding to the taint key. Must be a label value.",
	"effect":    "The effect of the taint on claims that do not tolerate the taint and through such claims on the pods using them. Valid effects are NoSchedule and NoExecute. PreferNoSchedule as used for nodes is not valid here.",
	"timeAdded": "TimeAdded represents the time at which the taint was added. Added automatically during create or update if not set.",
}

func (DeviceTaint) SwaggerDoc() map[string]string {
	return map_DeviceTaint
}

var map_DeviceTaintRule = map[string]string{
	"":         "DeviceTaintRule adds one taint to all devices which match the selector. This has the same effect as if the taint was specified directly in the ResourceSlice by the DRA driver.",
	"metadata": "Standard object metadata",
	"spec":     "Spec specifies the selector and one taint.\n\nChanging the spec automatically increments the metadata.generation number.",
}

func (DeviceTaintRule) SwaggerDoc() map[string]string {
	return map_DeviceTaintRule
}

var map_DeviceTaintRuleList = map[string]string{
	"":         "DeviceTaintRuleList is a collection of DeviceTaintRules.",
	"metadata": "Standard list metadata",
	"items":    "Items is the list of DeviceTaintRules.",
}

func (DeviceTaintRuleList) SwaggerDoc() map[string]string {
	return map_DeviceTaintRuleList
}

var map_DeviceTaintRuleSpec = map[string]string{
	"":               "DeviceTaintRuleSpec specifies the selector and one taint.",
	"deviceSelector": "DeviceSelector defines which device(s) the taint is applied to. All selector criteria must be satified for a device to match. The empty selector matches all devices. Without a selector, no devices are matches.",
	"taint":          "The taint that gets applied to matching devices.",
}

func (DeviceTaintRuleSpec) SwaggerDoc() map[string]string {
	return map_DeviceTaintRuleSpec
}

var map_DeviceTaintSelector = map[string]string{
	"":                "DeviceTaintSelector defines which device(s) a DeviceTaintRule applies to. The empty selector matches all devices. Without a selector, no devices are matched.",
	"deviceClassName": "If DeviceClassName is set, the selectors defined there must be satisfied by a device to be selected. This field corresponds to class.metadata.name.",
	"driver":          "If driver is set, only devices from that driver are selected. This fields corresponds to slice.spec.driver.",
	"pool":            "If pool is set, only devices in that pool are selected.\n\nAlso setting the driver name may be useful to avoid ambiguity when different drivers use the same pool name, but this is not required because selecting pools from different drivers may also be useful, for example when drivers with node-local devices use the node name as their pool name.",
	"device":          "If device is set, only devices with that name are selected. This field corresponds to slice.spec.devices[].name.\n\nSetting also driver and pool may be required to avoid ambiguity, but is not required.",
	"selectors":       "Selectors contains the same selection criteria as a ResourceClaim. Currently, CEL expressions are supported. All of these selectors must be satisfied.",
}

func (DeviceTaintSelector) SwaggerDoc() map[string]string {
	return map_DeviceTaintSelector
}

var map_DeviceToleration = map[string]string{
	"":                  "The ResourceClaim this DeviceToleration is attached to tolerates any taint that matches the triple <key,value,effect> using the matching operator <operator>.",
	"key":               "Key is the taint key that the toleration applies to. Empty means match all taint keys. If the key is empty, operator must be Exists; this combination means to match all values and all keys. Must be a label name.",
	"operator":          "Operator represents a key's relationship to the value. Valid operators are Exists and Equal. Defaults to Equal. Exists is equivalent to wildcard for value, so that a ResourceClaim can tolerate all taints of a particular category.",
	"value":             "Value is the taint value the toleration matches to. If the operator is Exists, the value must be empty, otherwise just a regular string. Must be a label value.",
	"effect":            "Effect indicates the taint effect to match. Empty means match all taint effects. When specified, allowed values are NoSchedule and NoExecute.",
	"tolerationSeconds": "TolerationSeconds represents the period of time the toleration (which must be of effect NoExecute, otherwise this field is ignored) tolerates the taint. By default, it is not set, which means tolerate the taint forever (do not evict). Zero and negative values will be treated as 0 (evict immediately) by the system. If larger than zero, the time when the pod needs to be evicted is calculated as <time when taint was adedd> + <toleration seconds>.",
}

func (DeviceToleration) SwaggerDoc() map[string]string {
	return map_DeviceToleration
}

var map_NetworkDeviceData = map[string]string{
	"":                "NetworkDeviceData provides network-related details for the allocated device. This information may be filled by drivers or other components to configure or identify the device within a network context.",
	"interfaceName":   "InterfaceName specifies the name of the network interface associated with the allocated device. This might be the name of a physical or virtual network interface being configured in the pod.\n\nMust not be longer than 256 characters.",
	"ips":             "IPs lists the network addresses assigned to the device's network interface. This can include both IPv4 and IPv6 addresses. The IPs are in the CIDR notation, which includes both the address and the associated subnet mask. e.g.: \"*********/24\" for IPv4 and \"2001:db8::5/64\" for IPv6.\n\nMust not contain more than 16 entries.",
	"hardwareAddress": "HardwareAddress represents the hardware address (e.g. MAC Address) of the device's network interface.\n\nMust not be longer than 128 characters.",
}

func (NetworkDeviceData) SwaggerDoc() map[string]string {
	return map_NetworkDeviceData
}

var map_OpaqueDeviceConfiguration = map[string]string{
	"":           "OpaqueDeviceConfiguration contains configuration parameters for a driver in a format defined by the driver vendor.",
	"driver":     "Driver is used to determine which kubelet plugin needs to be passed these configuration parameters.\n\nAn admission policy provided by the driver developer could use this to decide whether it needs to validate them.\n\nMust be a DNS subdomain and should end with a DNS domain owned by the vendor of the driver.",
	"parameters": "Parameters can contain arbitrary data. It is the responsibility of the driver developer to handle validation and versioning. Typically this includes self-identification and a version (\"kind\" + \"apiVersion\" for Kubernetes types), with conversion between different versions.\n\nThe length of the raw data must be smaller or equal to 10 Ki.",
}

func (OpaqueDeviceConfiguration) SwaggerDoc() map[string]string {
	return map_OpaqueDeviceConfiguration
}

var map_ResourceClaim = map[string]string{
	"":         "ResourceClaim describes a request for access to resources in the cluster, for use by workloads. For example, if a workload needs an accelerator device with specific properties, this is how that request is expressed. The status stanza tracks whether this claim has been satisfied and what specific resources have been allocated.\n\nThis is an alpha type and requires enabling the DynamicResourceAllocation feature gate.",
	"metadata": "Standard object metadata",
	"spec":     "Spec describes what is being requested and how to configure it. The spec is immutable.",
	"status":   "Status describes whether the claim is ready to use and what has been allocated.",
}

func (ResourceClaim) SwaggerDoc() map[string]string {
	return map_ResourceClaim
}

var map_ResourceClaimConsumerReference = map[string]string{
	"":         "ResourceClaimConsumerReference contains enough information to let you locate the consumer of a ResourceClaim. The user must be a resource in the same namespace as the ResourceClaim.",
	"apiGroup": "APIGroup is the group for the resource being referenced. It is empty for the core API. This matches the group in the APIVersion that is used when creating the resources.",
	"resource": "Resource is the type of resource being referenced, for example \"pods\".",
	"name":     "Name is the name of resource being referenced.",
	"uid":      "UID identifies exactly one incarnation of the resource.",
}

func (ResourceClaimConsumerReference) SwaggerDoc() map[string]string {
	return map_ResourceClaimConsumerReference
}

var map_ResourceClaimList = map[string]string{
	"":         "ResourceClaimList is a collection of claims.",
	"metadata": "Standard list metadata",
	"items":    "Items is the list of resource claims.",
}

func (ResourceClaimList) SwaggerDoc() map[string]string {
	return map_ResourceClaimList
}

var map_ResourceClaimSpec = map[string]string{
	"":        "ResourceClaimSpec defines what is being requested in a ResourceClaim and how to configure it.",
	"devices": "Devices defines how to request devices.",
}

func (ResourceClaimSpec) SwaggerDoc() map[string]string {
	return map_ResourceClaimSpec
}

var map_ResourceClaimStatus = map[string]string{
	"":            "ResourceClaimStatus tracks whether the resource has been allocated and what the result of that was.",
	"allocation":  "Allocation is set once the claim has been allocated successfully.",
	"reservedFor": "ReservedFor indicates which entities are currently allowed to use the claim. A Pod which references a ResourceClaim which is not reserved for that Pod will not be started. A claim that is in use or might be in use because it has been reserved must not get deallocated.\n\nIn a cluster with multiple scheduler instances, two pods might get scheduled concurrently by different schedulers. When they reference the same ResourceClaim which already has reached its maximum number of consumers, only one pod can be scheduled.\n\nBoth schedulers try to add their pod to the claim.status.reservedFor field, but only the update that reaches the API server first gets stored. The other one fails with an error and the scheduler which issued it knows that it must put the pod back into the queue, waiting for the ResourceClaim to become usable again.\n\nThere can be at most 256 such reservations. This may get increased in the future, but not reduced.",
	"devices":     "Devices contains the status of each device allocated for this claim, as reported by the driver. This can include driver-specific information. Entries are owned by their respective drivers.",
}

func (ResourceClaimStatus) SwaggerDoc() map[string]string {
	return map_ResourceClaimStatus
}

var map_ResourceClaimTemplate = map[string]string{
	"":         "ResourceClaimTemplate is used to produce ResourceClaim objects.\n\nThis is an alpha type and requires enabling the DynamicResourceAllocation feature gate.",
	"metadata": "Standard object metadata",
	"spec":     "Describes the ResourceClaim that is to be generated.\n\nThis field is immutable. A ResourceClaim will get created by the control plane for a Pod when needed and then not get updated anymore.",
}

func (ResourceClaimTemplate) SwaggerDoc() map[string]string {
	return map_ResourceClaimTemplate
}

var map_ResourceClaimTemplateList = map[string]string{
	"":         "ResourceClaimTemplateList is a collection of claim templates.",
	"metadata": "Standard list metadata",
	"items":    "Items is the list of resource claim templates.",
}

func (ResourceClaimTemplateList) SwaggerDoc() map[string]string {
	return map_ResourceClaimTemplateList
}

var map_ResourceClaimTemplateSpec = map[string]string{
	"":         "ResourceClaimTemplateSpec contains the metadata and fields for a ResourceClaim.",
	"metadata": "ObjectMeta may contain labels and annotations that will be copied into the ResourceClaim when creating it. No other fields are allowed and will be rejected during validation.",
	"spec":     "Spec for the ResourceClaim. The entire content is copied unchanged into the ResourceClaim that gets created from this template. The same fields as in a ResourceClaim are also valid here.",
}

func (ResourceClaimTemplateSpec) SwaggerDoc() map[string]string {
	return map_ResourceClaimTemplateSpec
}

var map_ResourcePool = map[string]string{
	"":                   "ResourcePool describes the pool that ResourceSlices belong to.",
	"name":               "Name is used to identify the pool. For node-local devices, this is often the node name, but this is not required.\n\nIt must not be longer than 253 characters and must consist of one or more DNS sub-domains separated by slashes. This field is immutable.",
	"generation":         "Generation tracks the change in a pool over time. Whenever a driver changes something about one or more of the resources in a pool, it must change the generation in all ResourceSlices which are part of that pool. Consumers of ResourceSlices should only consider resources from the pool with the highest generation number. The generation may be reset by drivers, which should be fine for consumers, assuming that all ResourceSlices in a pool are updated to match or deleted.\n\nCombined with ResourceSliceCount, this mechanism enables consumers to detect pools which are comprised of multiple ResourceSlices and are in an incomplete state.",
	"resourceSliceCount": "ResourceSliceCount is the total number of ResourceSlices in the pool at this generation number. Must be greater than zero.\n\nConsumers can use this to check whether they have seen all ResourceSlices belonging to the same pool.",
}

func (ResourcePool) SwaggerDoc() map[string]string {
	return map_ResourcePool
}

var map_ResourceSlice = map[string]string{
	"":         "ResourceSlice represents one or more resources in a pool of similar resources, managed by a common driver. A pool may span more than one ResourceSlice, and exactly how many ResourceSlices comprise a pool is determined by the driver.\n\nAt the moment, the only supported resources are devices with attributes and capacities. Each device in a given pool, regardless of how many ResourceSlices, must have a unique name. The ResourceSlice in which a device gets published may change over time. The unique identifier for a device is the tuple <driver name>, <pool name>, <device name>.\n\nWhenever a driver needs to update a pool, it increments the pool.Spec.Pool.Generation number and updates all ResourceSlices with that new number and new resource definitions. A consumer must only use ResourceSlices with the highest generation number and ignore all others.\n\nWhen allocating all resources in a pool matching certain criteria or when looking for the best solution among several different alternatives, a consumer should check the number of ResourceSlices in a pool (included in each ResourceSlice) to determine whether its view of a pool is complete and if not, should wait until the driver has completed updating the pool.\n\nFor resources that are not local to a node, the node name is not set. Instead, the driver may use a node selector to specify where the devices are available.\n\nThis is an alpha type and requires enabling the DynamicResourceAllocation feature gate.",
	"metadata": "Standard object metadata",
	"spec":     "Contains the information published by the driver.\n\nChanging the spec automatically increments the metadata.generation number.",
}

func (ResourceSlice) SwaggerDoc() map[string]string {
	return map_ResourceSlice
}

var map_ResourceSliceList = map[string]string{
	"":         "ResourceSliceList is a collection of ResourceSlices.",
	"metadata": "Standard list metadata",
	"items":    "Items is the list of resource ResourceSlices.",
}

func (ResourceSliceList) SwaggerDoc() map[string]string {
	return map_ResourceSliceList
}

var map_ResourceSliceSpec = map[string]string{
	"":                       "ResourceSliceSpec contains the information published by the driver in one ResourceSlice.",
	"driver":                 "Driver identifies the DRA driver providing the capacity information. A field selector can be used to list only ResourceSlice objects with a certain driver name.\n\nMust be a DNS subdomain and should end with a DNS domain owned by the vendor of the driver. This field is immutable.",
	"pool":                   "Pool describes the pool that this ResourceSlice belongs to.",
	"nodeName":               "NodeName identifies the node which provides the resources in this pool. A field selector can be used to list only ResourceSlice objects belonging to a certain node.\n\nThis field can be used to limit access from nodes to ResourceSlices with the same node name. It also indicates to autoscalers that adding new nodes of the same type as some old node might also make new resources available.\n\nExactly one of NodeName, NodeSelector, AllNodes, and PerDeviceNodeSelection must be set. This field is immutable.",
	"nodeSelector":           "NodeSelector defines which nodes have access to the resources in the pool, when that pool is not limited to a single node.\n\nMust use exactly one term.\n\nExactly one of NodeName, NodeSelector, AllNodes, and PerDeviceNodeSelection must be set.",
	"allNodes":               "AllNodes indicates that all nodes have access to the resources in the pool.\n\nExactly one of NodeName, NodeSelector, AllNodes, and PerDeviceNodeSelection must be set.",
	"devices":                "Devices lists some or all of the devices in this pool.\n\nMust not have more than 128 entries.",
	"perDeviceNodeSelection": "PerDeviceNodeSelection defines whether the access from nodes to resources in the pool is set on the ResourceSlice level or on each device. If it is set to true, every device defined the ResourceSlice must specify this individually.\n\nExactly one of NodeName, NodeSelector, AllNodes, and PerDeviceNodeSelection must be set.",
	"sharedCounters":         "SharedCounters defines a list of counter sets, each of which has a name and a list of counters available.\n\nThe names of the SharedCounters must be unique in the ResourceSlice.\n\nThe maximum number of SharedCounters is 32.",
}

func (ResourceSliceSpec) SwaggerDoc() map[string]string {
	return map_ResourceSliceSpec
}

// AUTO-GENERATED FUNCTIONS END HERE
