/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1beta2

import (
	resourcev1beta2 "k8s.io/api/resource/v1beta2"
)

// DeviceConstraintApplyConfiguration represents a declarative configuration of the DeviceConstraint type for use
// with apply.
type DeviceConstraintApplyConfiguration struct {
	Requests       []string                            `json:"requests,omitempty"`
	MatchAttribute *resourcev1beta2.FullyQualifiedName `json:"matchAttribute,omitempty"`
}

// DeviceConstraintApplyConfiguration constructs a declarative configuration of the DeviceConstraint type for use with
// apply.
func DeviceConstraint() *DeviceConstraintApplyConfiguration {
	return &DeviceConstraintApplyConfiguration{}
}

// WithRequests adds the given value to the Requests field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the Requests field.
func (b *DeviceConstraintApplyConfiguration) WithRequests(values ...string) *DeviceConstraintApplyConfiguration {
	for i := range values {
		b.Requests = append(b.Requests, values[i])
	}
	return b
}

// WithMatchAttribute sets the MatchAttribute field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the MatchAttribute field is set to the value of the last call.
func (b *DeviceConstraintApplyConfiguration) WithMatchAttribute(value resourcev1beta2.FullyQualifiedName) *DeviceConstraintApplyConfiguration {
	b.MatchAttribute = &value
	return b
}
