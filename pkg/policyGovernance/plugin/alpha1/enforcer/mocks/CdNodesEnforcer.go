// Code generated by mockery v2.42.0. DO NOT EDIT.

package mocks

import (
	bean "github.com/devtron-labs/devtron/pkg/policyGovernance/plugin/alpha1/bean"

	globalPolicybean "github.com/devtron-labs/devtron/pkg/globalPolicy/bean"

	mock "github.com/stretchr/testify/mock"

	pipeline "github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig/bean/pipeline"

	resourceQualifiers "github.com/devtron-labs/devtron/pkg/resourceQualifiers"
)

// CdEnforcer is an autogenerated mock type for the CdEnforcer type
type CdEnforcer struct {
	mock.Mock
}

// Enforce provides a mock function with given fields: pipelineIds, enforcementRequest
func (_m *CdEnforcer) Enforce(pipelineIds []int, enforcementRequest *bean.PluginEnforcementRequest) (map[int]map[bean.PluginApplyStage][]*bean.RuleWithSource, map[int][]*bean.RuleWithSource, error) {
	ret := _m.Called(pipelineIds, enforcementRequest)

	if len(ret) == 0 {
		panic("no return value specified for Enforce")
	}

	var r0 map[int]map[bean.PluginApplyStage][]*bean.RuleWithSource
	var r1 map[int][]*bean.RuleWithSource
	var r2 error
	if rf, ok := ret.Get(0).(func([]int, *bean.PluginEnforcementRequest) (map[int]map[bean.PluginApplyStage][]*bean.RuleWithSource, map[int][]*bean.RuleWithSource, error)); ok {
		return rf(pipelineIds, enforcementRequest)
	}
	if rf, ok := ret.Get(0).(func([]int, *bean.PluginEnforcementRequest) map[int]map[bean.PluginApplyStage][]*bean.RuleWithSource); ok {
		r0 = rf(pipelineIds, enforcementRequest)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[int]map[bean.PluginApplyStage][]*bean.RuleWithSource)
		}
	}

	if rf, ok := ret.Get(1).(func([]int, *bean.PluginEnforcementRequest) map[int][]*bean.RuleWithSource); ok {
		r1 = rf(pipelineIds, enforcementRequest)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).(map[int][]*bean.RuleWithSource)
		}
	}

	if rf, ok := ret.Get(2).(func([]int, *bean.PluginEnforcementRequest) error); ok {
		r2 = rf(pipelineIds, enforcementRequest)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// EnforcedPluginsOnNewCD provides a mock function with given fields: _a0, branches, CriteriaMappings
func (_m *CdEnforcer) EnforcedPluginsOnNewCD(_a0 *pipeline.PipelineMin, branches []globalPolicybean.BranchDto, CriteriaMappings []*resourceQualifiers.CriteriaQualifierMappingModel) (map[bean.PluginApplyStage][]*bean.RuleWithSource, []*bean.RuleWithSource, error) {
	ret := _m.Called(_a0, branches, CriteriaMappings)

	if len(ret) == 0 {
		panic("no return value specified for EnforcedPluginsOnNewCD")
	}

	var r0 map[bean.PluginApplyStage][]*bean.RuleWithSource
	var r1 []*bean.RuleWithSource
	var r2 error
	if rf, ok := ret.Get(0).(func(*pipeline.PipelineMin, []globalPolicybean.BranchDto, []*resourceQualifiers.CriteriaQualifierMappingModel) (map[bean.PluginApplyStage][]*bean.RuleWithSource, []*bean.RuleWithSource, error)); ok {
		return rf(_a0, branches, CriteriaMappings)
	}
	if rf, ok := ret.Get(0).(func(*pipeline.PipelineMin, []globalPolicybean.BranchDto, []*resourceQualifiers.CriteriaQualifierMappingModel) map[bean.PluginApplyStage][]*bean.RuleWithSource); ok {
		r0 = rf(_a0, branches, CriteriaMappings)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[bean.PluginApplyStage][]*bean.RuleWithSource)
		}
	}

	if rf, ok := ret.Get(1).(func(*pipeline.PipelineMin, []globalPolicybean.BranchDto, []*resourceQualifiers.CriteriaQualifierMappingModel) []*bean.RuleWithSource); ok {
		r1 = rf(_a0, branches, CriteriaMappings)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).([]*bean.RuleWithSource)
		}
	}

	if rf, ok := ret.Get(2).(func(*pipeline.PipelineMin, []globalPolicybean.BranchDto, []*resourceQualifiers.CriteriaQualifierMappingModel) error); ok {
		r2 = rf(_a0, branches, CriteriaMappings)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// NewCdEnforcer creates a new instance of CdEnforcer. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewCdEnforcer(t interface {
	mock.TestingT
	Cleanup(func())
}) *CdEnforcer {
	mock := &CdEnforcer{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
