/*
 * Copyright (c) 2024. Devtron Inc.
 */

package restHandler

import (
	"encoding/json"
	"github.com/devtron-labs/devtron/pkg/apiToken/bean"
)

func (impl NotificationRestHandlerImpl) createDraftRequest(token string) (*bean.DraftApprovalRequest, error) {
	claimBytes, err := impl.userAuthService.GetFieldValuesFromToken(token)
	if err != nil {
		return nil, err
	}
	return CreateDraftApprovalRequest(claimBytes), err
}

func (impl NotificationRestHandlerImpl) createDeploymentApprovalRequest(token string) (*bean.DeploymentApprovalRequest, error) {
	claimBytes, err := impl.userAuthService.GetFieldValuesFromToken(token)
	if err != nil {
		return nil, err
	}
	return CreateDeploymentApprovalRequest(claimBytes), err
}

func CreateDraftApprovalRequest(jsonStr []byte) *bean.DraftApprovalRequest {
	draftApprovalRequest := &bean.DraftApprovalRequest{}
	json.Unmarshal(jsonStr, draftApprovalRequest)
	return draftApprovalRequest
}

func CreateDeploymentApprovalRequest(jsonStr []byte) *bean.DeploymentApprovalRequest {
	deploymentApprovalRequest := &bean.DeploymentApprovalRequest{}
	json.Unmarshal(jsonStr, deploymentApprovalRequest)
	return deploymentApprovalRequest
}
